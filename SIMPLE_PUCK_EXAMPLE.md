# Ejemplo Simple de PuckEditor

Este es un ejemplo sencillo e independiente de PuckEditor que demuestra las funcionalidades básicas de la biblioteca.

## 🚀 Cómo acceder al ejemplo

1. Inicia el servidor de desarrollo:
   ```bash
   npm run dev
   ```

2. Visita: `http://localhost:3001/simple-example`

## 📋 Características del ejemplo

### Componentes incluidos:

1. **SimpleHeading** - Títulos editables
   - Texto personalizable
   - Niveles H1, H2, H3
   - Color personalizable

2. **SimpleText** - Párrafos de texto
   - Contenido editable con textarea
   - Tamaños: pequeño, mediano, grande

3. **SimpleButton** - Botones interactivos
   - Texto personalizable
   - Estilos: primario y secundario
   - Funcionalidad onClick

4. **SimpleCard** - Tarjetas con contenido
   - Título y descripción editables
   - Color de fondo personalizable

### Funcionalidades:

- ✅ **Modo Vista**: Muestra el contenido renderizado
- ✅ **Modo <PERSON>ici<PERSON>**: Editor visual completo de PuckEditor
- ✅ **Drag & Drop**: Arrastra componentes para reorganizar
- ✅ **Panel de Propiedades**: Edita propiedades de cada componente
- ✅ **Guardar/Publicar**: Funciones para persistir cambios
- ✅ **Datos en Consola**: Los datos se muestran en la consola del navegador

## 🛠️ Estructura del código

### Archivo principal: `components/SimplePuckExample.tsx`

```typescript
// 1. Definición de tipos
type Props = {
  SimpleHeading: { text: string; level: "1" | "2" | "3"; color: string };
  // ... otros componentes
};

// 2. Configuración de Puck
const simpleConfig: Config<Props> = {
  components: {
    SimpleHeading: {
      label: "Título Simple",
      fields: { /* campos editables */ },
      defaultProps: { /* valores por defecto */ },
      render: ({ text, level, color }) => { /* JSX del componente */ }
    },
    // ... otros componentes
  }
};

// 3. Datos iniciales
const initialData: Data<Props> = {
  content: [ /* componentes iniciales */ ],
  root: {},
  zones: {}
};

// 4. Componente principal
export default function SimplePuckExample() {
  const [data, setData] = useState(initialData);
  const [isEditing, setIsEditing] = useState(false);
  
  return (
    <div>
      {/* Header con controles */}
      {isEditing ? (
        <Puck config={simpleConfig} data={data} onChange={setData} />
      ) : (
        <Render config={simpleConfig} data={data} />
      )}
    </div>
  );
}
```

## 🎯 Conceptos clave de PuckEditor

### 1. **Config**: Define los componentes disponibles
- `label`: Nombre que aparece en el editor
- `fields`: Campos editables (text, textarea, select, radio, etc.)
- `defaultProps`: Valores por defecto
- `render`: Función que renderiza el componente

### 2. **Data**: Estructura de datos del contenido
- `content`: Array de componentes en la página
- `root`: Propiedades globales
- `zones`: Áreas de drop personalizadas

### 3. **Componentes principales**:
- `<Puck>`: Editor completo
- `<Render>`: Solo renderiza el contenido
- `onChange`: Callback cuando cambian los datos
- `onPublish`: Callback para guardar/publicar

## 🔧 Personalización

### Agregar un nuevo componente:

1. **Definir el tipo** en `Props`:
```typescript
type Props = {
  // ... componentes existentes
  MiNuevoComponente: { 
    titulo: string; 
    activo: boolean; 
  };
};
```

2. **Agregar a la configuración**:
```typescript
const simpleConfig: Config<Props> = {
  components: {
    // ... componentes existentes
    MiNuevoComponente: {
      label: "Mi Componente",
      fields: {
        titulo: { type: "text", label: "Título" },
        activo: { type: "radio", options: [
          { value: true, label: "Sí" },
          { value: false, label: "No" }
        ]}
      },
      defaultProps: { titulo: "Nuevo", activo: true },
      render: ({ titulo, activo }) => (
        <div style={{ opacity: activo ? 1 : 0.5 }}>
          <h4>{titulo}</h4>
        </div>
      )
    }
  }
};
```

### Tipos de campos disponibles:
- `text`: Campo de texto simple
- `textarea`: Área de texto multilínea
- `select`: Lista desplegable
- `radio`: Botones de radio
- `checkbox`: Casilla de verificación
- `number`: Campo numérico
- `array`: Arrays de elementos
- `object`: Objetos anidados

## 📚 Recursos adicionales

- [Documentación oficial de PuckEditor](https://puckeditor.com/docs)
- [Ejemplos en GitHub](https://github.com/measuredco/puck)
- [API Reference](https://puckeditor.com/docs/api-reference)

## 💡 Próximos pasos

1. Experimenta agregando nuevos componentes
2. Prueba diferentes tipos de campos
3. Implementa persistencia con una base de datos
4. Agrega validaciones personalizadas
5. Crea componentes más complejos con slots/zones
