import { Metada<PERSON> } from "next";
import config from "../../config/server";
import { initialData } from "../../config/initial-data";
import { Props, RootProps } from "../../config/types";

import { Config } from "@/core";
import { Render, resolveAllData } from "@/core/rsc";

// NB This is only necessary for this demo app, as the `@/core/rsc` path does not resolve to dist but the type for Config does
// This will be resolved once the RSC package is merged with the regular package after DropZone support is dropped
const conf = config as unknown as Config;

export async function generateMetadata(): Promise<Metadata> {
  return {
    title: initialData["/"].root.title,
  };
}

export default async function Page() {
  const data = initialData["/"];
  const metadata = {
    example: "Hello, world",
  };

  const resolvedData = await resolveAllData<Props, RootProps>(
    data,
    conf,
    metadata
  );

  return <Render config={conf} data={resolvedData} metadata={metadata} />;
}
