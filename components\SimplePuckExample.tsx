"use client";

import { useState } from "react";
import { <PERSON><PERSON>, <PERSON><PERSON> } from "@measured/puck";
import type { Config, Data } from "@measured/puck";

// Definir los tipos de props para nuestros componentes
type Props = {
  SimpleHeading: {
    id: string;
    text: string;
    level: "1" | "2" | "3";
    color: string;
  };
  SimpleText: {
    id: string;
    content: string;
    size: "small" | "medium" | "large";
  };
  SimpleButton: {
    id: string;
    label: string;
    variant: "primary" | "secondary";
    onClick?: () => void;
  };
  SimpleCard: {
    id: string;
    title: string;
    description: string;
    backgroundColor: string;
  };
  FlexContainer: {
    id: string;
    direction: "row" | "column";
    justifyContent: "flex-start" | "center" | "flex-end" | "space-between" | "space-around" | "space-evenly";
    alignItems: "flex-start" | "center" | "flex-end" | "stretch";
    gap: string;
    padding: string;
    backgroundColor: string;
  };
  GridContainer: {
    id: string;
    columnCount: number;
    customColumns: string;
    useCustomColumns: boolean;
    rows: string;
    gap: string;
    padding: string;
    backgroundColor: string;
  };
};

// Configuración simple de Puck
const simpleConfig: Config<Props> = {
  components: {
    SimpleHeading: {
      label: "Título Simple",
      fields: {
        text: {
          type: "text",
          label: "Texto del título"
        },
        level: {
          type: "select",
          label: "Nivel",
          options: [
            { value: "1", label: "H1" },
            { value: "2", label: "H2" },
            { value: "3", label: "H3" },
          ],
        },
        color: {
          type: "text",
          label: "Color (CSS)"
        }
      },
      defaultProps: {
        id: "SimpleHeading-default",
        text: "Mi Título",
        level: "2",
        color: "#333333"
      },
      render: ({ text, level, color }) => {
        const Tag = `h${level}` as keyof JSX.IntrinsicElements;
        return (
          <Tag style={{ 
            color, 
            margin: "16px 0",
            fontFamily: "Arial, sans-serif"
          }}>
            {text}
          </Tag>
        );
      },
    },
    
    SimpleText: {
      label: "Texto Simple",
      fields: {
        content: {
          type: "textarea",
          label: "Contenido"
        },
        size: {
          type: "radio",
          label: "Tamaño",
          options: [
            { value: "small", label: "Pequeño" },
            { value: "medium", label: "Mediano" },
            { value: "large", label: "Grande" },
          ],
        },
      },
      defaultProps: {
        id: "SimpleText-default",
        content: "Este es un texto de ejemplo.",
        size: "medium"
      },
      render: ({ content, size }) => {
        const sizeMap = {
          small: "14px",
          medium: "16px",
          large: "20px"
        };
        
        return (
          <p style={{ 
            fontSize: sizeMap[size],
            lineHeight: 1.5,
            margin: "12px 0",
            fontFamily: "Arial, sans-serif"
          }}>
            {content}
          </p>
        );
      },
    },

    SimpleButton: {
      label: "Botón Simple",
      fields: {
        label: {
          type: "text",
          label: "Texto del botón"
        },
        variant: {
          type: "radio",
          label: "Estilo",
          options: [
            { value: "primary", label: "Primario" },
            { value: "secondary", label: "Secundario" },
          ],
        },
      },
      defaultProps: {
        id: "SimpleButton-default",
        label: "Hacer clic",
        variant: "primary"
      },
      render: ({ label, variant }) => {
        const styles = {
          primary: {
            backgroundColor: "#007bff",
            color: "white",
            border: "none"
          },
          secondary: {
            backgroundColor: "transparent",
            color: "#007bff",
            border: "2px solid #007bff"
          }
        };

        return (
          <button
            style={{
              ...styles[variant],
              padding: "12px 24px",
              borderRadius: "6px",
              cursor: "pointer",
              fontSize: "16px",
              fontFamily: "Arial, sans-serif",
              margin: "8px 0"
            }}
            onClick={() => alert(`¡Hiciste clic en: ${label}!`)}
          >
            {label}
          </button>
        );
      },
    },

    SimpleCard: {
      label: "Tarjeta Simple",
      fields: {
        title: {
          type: "text",
          label: "Título de la tarjeta"
        },
        description: {
          type: "textarea",
          label: "Descripción"
        },
        backgroundColor: {
          type: "text",
          label: "Color de fondo"
        }
      },
      defaultProps: {
        id: "SimpleCard-default",
        title: "Mi Tarjeta",
        description: "Esta es una descripción de ejemplo para la tarjeta.",
        backgroundColor: "#f8f9fa"
      },
      render: ({ title, description, backgroundColor }) => {
        return (
          <div style={{
            backgroundColor,
            border: "1px solid #dee2e6",
            borderRadius: "8px",
            padding: "20px",
            margin: "16px 0",
            boxShadow: "0 2px 4px rgba(0,0,0,0.1)"
          }}>
            <h3 style={{ 
              margin: "0 0 12px 0", 
              color: "#333",
              fontFamily: "Arial, sans-serif"
            }}>
              {title}
            </h3>
            <p style={{ 
              margin: 0, 
              color: "#666",
              lineHeight: 1.5,
              fontFamily: "Arial, sans-serif"
            }}>
              {description}
            </p>
          </div>
        );
      },
    },

    FlexContainer: {
      label: "Contenedor Flex",
      fields: {
        direction: {
          type: "radio",
          label: "Dirección",
          options: [
            { value: "row", label: "Horizontal" },
            { value: "column", label: "Vertical" },
          ],
        },
        justifyContent: {
          type: "select",
          label: "Justificación",
          options: [
            { value: "flex-start", label: "Inicio" },
            { value: "center", label: "Centro" },
            { value: "flex-end", label: "Final" },
            { value: "space-between", label: "Espacio entre" },
            { value: "space-around", label: "Espacio alrededor" },
            { value: "space-evenly", label: "Espacio uniforme" },
          ],
        },
        alignItems: {
          type: "select",
          label: "Alineación",
          options: [
            { value: "flex-start", label: "Inicio" },
            { value: "center", label: "Centro" },
            { value: "flex-end", label: "Final" },
            { value: "stretch", label: "Estirar" },
          ],
        },
        gap: {
          type: "text",
          label: "Espaciado (ej: 16px)"
        },
        padding: {
          type: "text",
          label: "Padding (ej: 20px)"
        },
        backgroundColor: {
          type: "text",
          label: "Color de fondo"
        }
      },
      defaultProps: {
        id: "FlexContainer-default",
        direction: "row",
        justifyContent: "flex-start",
        alignItems: "center",
        gap: "16px",
        padding: "20px",
        backgroundColor: "#f8f9fa"
      },
      render: ({ direction, justifyContent, alignItems, gap, padding, backgroundColor, puck }) => {
        const { renderDropZone } = puck || {};

        // Debug: Log de valores para verificar
        console.log("FlexContainer render:", { direction, justifyContent, alignItems, gap, padding, backgroundColor });

        return (
          <div
            className="custom-flex-container"
            data-direction={direction || "row"}
            style={{
              display: "flex",
              flexDirection: direction || "row",
              justifyContent: justifyContent || "flex-start",
              alignItems: alignItems || "center",
              gap: gap || "16px",
              padding: padding || "20px",
              backgroundColor: backgroundColor || "#f8f9fa",
              border: "2px dashed #dee2e6",
              borderRadius: "8px",
              minHeight: "100px",
              position: "relative",
              width: "100%",
              flexWrap: "nowrap"
            }}>
            {renderDropZone ? renderDropZone({ zone: "flex-items" }) : (
              <div style={{
                color: "#6c757d",
                fontStyle: "italic",
                textAlign: "center",
                width: "100%",
                padding: "20px"
              }}>
                Arrastra componentes aquí (Dirección: {direction || "row"})
              </div>
            )}
          </div>
        );
      },
    },

    GridContainer: {
      label: "Contenedor Grid",
      fields: {
        useCustomColumns: {
          type: "radio",
          label: "Tipo de configuración",
          options: [
            { value: false, label: "Número de columnas" },
            { value: true, label: "CSS personalizado" },
          ],
        },
        columnCount: {
          type: "select",
          label: "Número de columnas",
          options: [
            { value: 1, label: "1 columna" },
            { value: 2, label: "2 columnas" },
            { value: 3, label: "3 columnas" },
            { value: 4, label: "4 columnas" },
            { value: 5, label: "5 columnas" },
            { value: 6, label: "6 columnas" },
          ]
        },
        customColumns: {
          type: "text",
          label: "CSS personalizado (ej: 200px 1fr 100px)"
        },
        rows: {
          type: "text",
          label: "Filas (ej: auto)"
        },
        gap: {
          type: "text",
          label: "Espaciado (ej: 16px)"
        },
        padding: {
          type: "text",
          label: "Padding (ej: 20px)"
        },
        backgroundColor: {
          type: "text",
          label: "Color de fondo"
        }
      },
      defaultProps: {
        id: "GridContainer-default",
        columnCount: 3,
        customColumns: "repeat(3, 1fr)",
        useCustomColumns: false,
        rows: "auto",
        gap: "16px",
        padding: "20px",
        backgroundColor: "#f8f9fa"
      },
      render: ({ columnCount, customColumns, useCustomColumns, rows, gap, padding, backgroundColor, puck }) => {
        const { renderDropZone } = puck || {};

        // Valores por defecto explícitos
        const safeColumnCount = columnCount || 3;
        const safeCustomColumns = customColumns || "repeat(3, 1fr)";
        const safeUseCustomColumns = useCustomColumns || false;

        // Generar CSS de columnas basado en la configuración
        const gridTemplateColumns = safeUseCustomColumns
          ? safeCustomColumns
          : `repeat(${safeColumnCount}, 1fr)`;

        // Debug: Log de valores para verificar
        console.log("GridContainer render:", {
          columnCount, customColumns, useCustomColumns,
          safeColumnCount, safeCustomColumns, safeUseCustomColumns,
          gridTemplateColumns
        });

        return (
          <div
            className="custom-grid-container"
            data-columns={gridTemplateColumns}
            style={{
              display: "grid",
              gridTemplateColumns: gridTemplateColumns,
              gridTemplateRows: rows || "auto",
              gap: gap || "16px",
              padding: padding || "20px",
              backgroundColor: backgroundColor || "#f8f9fa",
              border: "2px dashed #dee2e6",
              borderRadius: "8px",
              minHeight: "100px",
              position: "relative",
              width: "100%"
            }}>
            {renderDropZone ? renderDropZone({ zone: "grid-items" }) : (
              <div style={{
                color: "#6c757d",
                fontStyle: "italic",
                textAlign: "center",
                gridColumn: "1 / -1",
                padding: "20px"
              }}>
                Arrastra componentes aquí ({safeUseCustomColumns ? safeCustomColumns : `${safeColumnCount} columnas`})
              </div>
            )}
          </div>
        );
      },
    },
  },
};

// Datos iniciales para el ejemplo
const initialData: Data<Props> = {
  content: [
    {
      type: "SimpleHeading",
      props: {
        id: "SimpleHeading-1",
        text: "¡Bienvenido a mi Editor Simple!",
        level: "1",
        color: "#2c3e50"
      }
    },
    {
      type: "SimpleText",
      props: {
        id: "SimpleText-1",
        content: "Este es un ejemplo sencillo de PuckEditor. Puedes editar este contenido usando el panel lateral.",
        size: "medium"
      }
    },
    {
      type: "SimpleCard",
      props: {
        id: "SimpleCard-1",
        title: "Tarjeta de Ejemplo",
        description: "Esta tarjeta muestra cómo puedes crear componentes personalizados con campos editables.",
        backgroundColor: "#e3f2fd"
      }
    },
    {
      type: "SimpleButton",
      props: {
        id: "SimpleButton-1",
        label: "¡Pruébame!",
        variant: "primary"
      }
    },
    {
      type: "FlexContainer",
      props: {
        id: "FlexContainer-1",
        direction: "row",
        justifyContent: "space-between",
        alignItems: "center",
        gap: "20px",
        padding: "24px",
        backgroundColor: "#e3f2fd"
      }
    },
    {
      type: "GridContainer",
      props: {
        id: "GridContainer-1",
        columnCount: 3,
        customColumns: "repeat(3, 1fr)",
        useCustomColumns: false,
        rows: "auto",
        gap: "16px",
        padding: "20px",
        backgroundColor: "#f3e5f5"
      }
    }
  ],
  root: {},
  zones: {}
};

export default function SimplePuckExample() {
  const [data, setData] = useState<Data<Props>>(initialData);
  const [isEditing, setIsEditing] = useState(false);

  // Timestamp para forzar actualización - v2.3 con CSS override
  console.log("SimplePuckExample cargado - v2.3 con CSS override:", new Date().toISOString());

  return (
    <div style={{
      fontFamily: "Arial, sans-serif",
      minHeight: "100vh"
    }}>
      {/* CSS personalizado para sobrescribir estilos de PuckEditor */}
      <style dangerouslySetInnerHTML={{
        __html: `
          /* Sobrescribir estilos de FlexContainer */
          .custom-flex-container {
            display: flex !important;
            flex-wrap: nowrap !important;
          }
          .custom-flex-container[data-direction="row"] {
            flex-direction: row !important;
          }
          .custom-flex-container[data-direction="column"] {
            flex-direction: column !important;
          }

          /* Sobrescribir estilos de GridContainer */
          .custom-grid-container {
            display: grid !important;
          }
          .custom-grid-container[data-columns*="repeat(1"] {
            grid-template-columns: repeat(1, 1fr) !important;
          }
          .custom-grid-container[data-columns*="repeat(2"] {
            grid-template-columns: repeat(2, 1fr) !important;
          }
          .custom-grid-container[data-columns*="repeat(3"] {
            grid-template-columns: repeat(3, 1fr) !important;
          }
          .custom-grid-container[data-columns*="repeat(4"] {
            grid-template-columns: repeat(4, 1fr) !important;
          }
          .custom-grid-container[data-columns*="repeat(5"] {
            grid-template-columns: repeat(5, 1fr) !important;
          }
          .custom-grid-container[data-columns*="repeat(6"] {
            grid-template-columns: repeat(6, 1fr) !important;
          }

          /* Sobrescribir clases específicas de PuckEditor si existen */
          .custom-flex-container.Flex,
          .custom-flex-container .Flex {
            flex-direction: inherit !important;
            flex-wrap: inherit !important;
          }
          .custom-grid-container.Grid,
          .custom-grid-container .Grid {
            display: grid !important;
            grid-template-columns: inherit !important;
          }
        `
      }} />

      {/* Header con controles */}
      <div style={{
        backgroundColor: "#f8f9fa",
        padding: "16px",
        borderBottom: "1px solid #dee2e6",
        display: "flex",
        justifyContent: "space-between",
        alignItems: "center"
      }}>
        <h1 style={{ margin: 0, color: "#333" }}>
          Ejemplo Simple de PuckEditor
        </h1>
        <div>
          <button
            onClick={() => setIsEditing(!isEditing)}
            style={{
              backgroundColor: isEditing ? "#dc3545" : "#28a745",
              color: "white",
              border: "none",
              padding: "8px 16px",
              borderRadius: "4px",
              cursor: "pointer",
              marginRight: "8px"
            }}
          >
            {isEditing ? "Salir del Editor" : "Editar Contenido"}
          </button>
          {isEditing && (
            <button
              onClick={() => {
                console.log("Datos guardados:", data);
                alert("¡Contenido guardado! Revisa la consola para ver los datos.");
              }}
              style={{
                backgroundColor: "#007bff",
                color: "white",
                border: "none",
                padding: "8px 16px",
                borderRadius: "4px",
                cursor: "pointer"
              }}
            >
              Guardar
            </button>
          )}
        </div>
      </div>

      {/* Contenido principal */}
      <div style={{ height: "calc(100vh - 80px)" }}>
        {isEditing ? (
          <Puck
            config={simpleConfig}
            data={data}
            onChange={setData}
            onPublish={(newData) => {
              setData(newData);
              console.log("Publicado:", newData);
              alert("¡Contenido publicado exitosamente!");
            }}
          />
        ) : (
          <div style={{ 
            padding: "40px",
            maxWidth: "800px",
            margin: "0 auto"
          }}>
            <Render config={simpleConfig} data={data} />
          </div>
        )}
      </div>
    </div>
  );
}
