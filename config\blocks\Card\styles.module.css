.Card {
  height: 100%;
}

.Card--card {
  background: white;
  box-shadow: rgba(140, 152, 164, 0.25) 0px 3px 6px 0px;
  border-radius: 8px;
  max-width: 100%;
}

.Card-inner {
  align-items: center;
  display: flex;
  gap: 16px;
  flex-direction: column;
}

.Card--card .Card-inner {
  align-items: flex-start;
  padding: 24px;
}

.Card-icon {
  border-radius: 256px;
  background: var(--puck-color-azure-09);
  color: var(--puck-color-azure-06);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
}

.Card-title {
  font-size: 22px;
  text-align: center;
}

.Card--card .Card-title {
  text-align: left;
}

.Card-description {
  font-size: 16px;
  line-height: 1.5;
  color: var(--puck-color-grey-05);
  text-align: center;
  font-weight: 300;
}

.Card--card .Card-description {
  text-align: left;
}
