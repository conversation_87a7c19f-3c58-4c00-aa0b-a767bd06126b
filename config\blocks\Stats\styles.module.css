.Stats-items {
  background-image: linear-gradient(
    120deg,
    var(--puck-color-azure-03) 0%,
    var(--puck-color-azure-05) 100%
  );
  border-radius: 24px;
  display: grid;
  grid-template-columns: 1fr;
  grid-gap: 72px;
  align-items: center;
  justify-content: space-between;
  margin: 0 auto;
  max-width: 768px;
  padding: 64px 16px;
}

@media (min-width: 768px) {
  .Stats-items {
    padding: 64px 24px;
  }
}

@media (min-width: 1024px) {
  .Stats-items {
    grid-template-columns: 1fr 1fr;
    padding: 128px 24px;
    max-width: 100%;
  }
}

.Stats-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  color: white;
  gap: 8px;
  overflow: hidden;
  width: 100%;
}

.Stats-icon {
  border-radius: 256px;
  background: var(--puck-color-azure-09);
  color: var(--puck-color-azure-06);
  display: flex;
  justify-content: center;
  align-items: center;
  width: 64px;
  height: 64px;
}

.Stats-label {
  font-size: 22px;
  text-align: center;
  font-weight: 600;
  opacity: 0.8;
}

.Stats-value {
  font-size: 72px;
  line-height: 1;
  font-weight: 700;
}
