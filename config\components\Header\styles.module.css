.Header {
  background-color: white;
  position: sticky;
  top: 0;
  z-index: 2;
}

.Header-inner {
  align-items: center;
  display: flex;
  margin-inline-start: auto;
  margin-inline-end: auto;
  max-width: 1280px;
  padding: 24px 16px;
}

@media (min-width: 768px) {
  .Header-inner {
    padding: 24px;
  }
}

.Header-logo {
  font-size: 24px;
  font-weight: 800;
  letter-spacing: 1.4;
  opacity: 0.8;
}

.Header-items {
  display: flex;
  gap: 24px;
  margin-inline-start: auto;
}

@media (min-width: 1024px) {
  .Header-items {
    gap: 32px;
  }
}
