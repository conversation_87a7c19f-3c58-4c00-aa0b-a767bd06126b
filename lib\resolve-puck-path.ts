const resolvePuckPath = (puckPath: string[] = []) => {
  const hasPath = puckPath.length > 0;

  const isEdit = hasPath ? puckPath[puckPath.length - 1] === "edit" : false;

  const pathArray = isEdit
    ? [...puckPath].slice(0, puckPath.length - 1)
    : [...puckPath];

  const path = `/${pathArray.join("/")}`;

  console.log("=== RESOLVE PUCK PATH DEBUG ===");
  console.log("puckPath input:", puckPath);
  console.log("hasPath:", hasPath);
  console.log("isEdit:", isEdit);
  console.log("pathArray:", pathArray);
  console.log("final path:", JSON.stringify(path));
  console.log("===============================");

  return {
    isEdit,
    path,
  };
};

export default resolvePuckPath;
