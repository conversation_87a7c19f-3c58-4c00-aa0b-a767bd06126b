{"name": "puckdemo", "version": "1.0.0", "private": true, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint"}, "dependencies": {"@measured/puck": "^0.19.1", "classnames": "^2.3.2", "css-box-model": "^1.2.1", "lucide-react": "^0.525.0", "next": "^15.2.4", "react": "^19.0.0", "react-dom": "^19.0.0", "react-from-json": "^0.8.0"}, "devDependencies": {"@types/node": "^17.0.12", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "eslint-config-custom": "*", "typescript": "^5.5.4"}}