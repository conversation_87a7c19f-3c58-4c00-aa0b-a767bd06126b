/**
 * ArrayField
 */

.Array<PERSON>ield {
  display: flex;
  flex-direction: column;
  background: var(--puck-color-azure-11);
  border: 1px solid var(--puck-color-grey-09);
  border-radius: 4px;
}

.ArrayField--isDraggingFrom {
  background-color: var(--puck-color-azure-11);
  overflow: hidden;
}

.ArrayField-addButton {
  background-color: var(--puck-color-white);
  border: none;
  border-radius: 3px;
  display: flex;
  color: var(--puck-color-azure-05);
  justify-content: center;
  cursor: pointer;
  width: 100%;
  margin: 0;
  padding: 14px; /* Retain same height as other items */
  text-align: left;
  transition: background-color 50ms ease-in;
}

.ArrayField--hasItems > .ArrayField-addButton {
  border-top: 1px solid var(--puck-color-grey-09);
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.ArrayField-addButton:focus-visible {
  outline: 2px solid var(--puck-color-azure-05);
  outline-offset: 2px;
  position: relative;
}

@media (hover: hover) and (pointer: fine) {
  .ArrayField:not(.ArrayField--isDraggingFrom) > .ArrayField-addButton:hover {
    background: var(--puck-color-azure-12);
    color: var(--puck-color-azure-04);
    transition: none;
  }
}

.ArrayField:not(.ArrayField--isDraggingFrom) > .ArrayField-addButton:active {
  background: var(--puck-color-azure-11);
  color: var(--puck-color-azure-04);
  transition: none;
}

.ArrayField-inner {
  margin-top: -1px;
}

/**
 * ArrayFieldItem
 */

.ArrayFieldItem {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
  display: block;
  position: relative;
}

.ArrayFieldItem {
  border-top: 1px solid var(--puck-color-grey-09);
}

.ArrayFieldItem--isDragging {
  border-top: transparent;
}

.ArrayFieldItem--isExpanded::before {
  display: none;
}

.ArrayFieldItem--isExpanded {
  border-bottom: 0;
  outline-offset: 0px !important; /* Important helps to override Nextra docs */
  outline: 1px solid var(--puck-color-azure-07) !important; /* Important helps to override Nextra docs */
  z-index: 2;
}

.ArrayFieldItem--isDragging {
  outline: 2px var(--puck-color-azure-09) solid !important;
}

.ArrayFieldItem--isDragging .ArrayFieldItem-summary:active {
  background-color: var(--puck-color-white);
}

.ArrayFieldItem + .ArrayFieldItem {
  border-top-left-radius: 0;
  border-top-right-radius: 0;
}

.ArrayFieldItem-summary {
  background: var(--puck-color-white);
  color: var(--puck-color-grey-04);
  cursor: pointer;
  display: flex;
  align-items: center;
  gap: 2px;
  justify-content: space-between;
  font-size: var(--puck-font-size-xxs);
  list-style: none;
  padding: 12px 15px;
  position: relative;
  overflow: hidden;
  transition: background-color 50ms ease-in;
}

.ArrayFieldItem:first-of-type > .ArrayFieldItem-summary {
  border-top-left-radius: 3px;
  border-top-right-radius: 3px;
}

.ArrayField--addDisabled
  > .ArrayField-inner
  > .ArrayFieldItem:last-of-type:not(.ArrayFieldItem--isExpanded)
  > .ArrayFieldItem-summary {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}

.ArrayField--addDisabled
  > .ArrayField-inner
  > .ArrayFieldItem--isExpanded:last-of-type {
  border-bottom-left-radius: 3px;
  border-bottom-right-radius: 3px;
}

.ArrayFieldItem-summary:focus-visible {
  outline: 2px solid var(--puck-color-azure-05);
  outline-offset: 2px;
}

@media (hover: hover) and (pointer: fine) {
  .ArrayFieldItem-summary:hover {
    background-color: var(--puck-color-azure-12);
    transition: none;
  }
}

.ArrayFieldItem-summary:active {
  background-color: var(--puck-color-azure-11);
  transition: none;
}

.ArrayFieldItem--isExpanded > .ArrayFieldItem-summary {
  background: var(--puck-color-azure-11);
  color: var(--puck-color-azure-04);
  font-weight: 600;
  transition: none;
}

.ArrayFieldItem-body {
  background: var(--puck-color-white);
  display: none;
}

.ArrayFieldItem--isExpanded > .ArrayFieldItem-body {
  display: block;
}

.ArrayFieldItem-fieldset {
  border: none;
  border-top: 1px solid var(--puck-color-grey-09);
  margin: 0;
  min-width: 0; /* Needed to ensure External input doesn't overflow, see https://stackoverflow.com/a/53784508 */
  padding: 16px 15px;
}

.ArrayFieldItem-rhs {
  display: flex;
  gap: 4px;
  align-items: center;
}

.ArrayFieldItem-actions {
  color: var(--puck-color-grey-04);
  display: flex;
  gap: 4px;
  opacity: 0;
}

.ArrayFieldItem-summary:focus-within
  > .ArrayFieldItem-rhs
  > .ArrayFieldItem-actions,
.ArrayFieldItem-summary:hover > .ArrayFieldItem-rhs > .ArrayFieldItem-actions {
  opacity: 1;
}
