.InputWrapper + .InputWrapper {
  margin-top: 12px;
}

.Input-label {
  align-items: center;
  color: var(--puck-color-grey-04);
  display: flex;
  padding-bottom: 12px;
  font-size: var(--puck-font-size-xxs);
  font-weight: 600;
}

.Input-labelIcon {
  color: var(--puck-color-grey-07);
  display: flex;
  margin-inline-end: 4px;
  padding-inline-start: 4px;
}

.Input-disabledIcon {
  color: var(--puck-color-grey-05);
  margin-inline-start: auto;
}

.Input-input {
  background: var(--puck-color-white);
  border-width: 1px;
  border-style: solid;
  border-color: var(--puck-color-grey-09);
  border-radius: 4px;
  box-sizing: border-box;
  font-family: inherit;
  font-size: 14px;
  padding: 12px 15px;
  transition: border-color 50ms ease-in;
  width: 100%;
  max-width: 100%;
}

select.Input-input {
  appearance: none; /* Safari */
  background: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%23c3c3c3'><polygon points='0,0 100,0 50,50'/></svg>")
    no-repeat;
  background-size: 12px;
  background-position: calc(100% - 12px) calc(50% + 3px);
  background-repeat: no-repeat;
  background-color: var(--puck-color-white);
  cursor: pointer;
}

select.Input-input:dir(rtl) {
  background-position: 12px calc(50% + 3px);
}

@media (hover: hover) and (pointer: fine) {
  .Input:has(> input):hover .Input-input:not([readonly]),
  .Input:has(> textarea):hover .Input-input:not([readonly]) {
    border-color: var(--puck-color-grey-05);
    transition: none;
  }
  .Input:has(> select):hover .Input-input:not([disabled]) {
    background-color: var(--puck-color-azure-12);
    background-image: url("data:image/svg+xml;utf8,<svg xmlns='http://www.w3.org/2000/svg' width='100' height='100' fill='%235a5a5a'><polygon points='0,0 100,0 50,50'/></svg>");
    border-color: var(--puck-color-grey-05);
    transition: none;
  }
}

.Input-input:focus {
  border-color: var(--puck-color-grey-05);
  outline: 2px solid var(--puck-color-azure-05);
  transition: none;
}

.Input--readOnly > .Input-input,
.Input--readOnly > select.Input-input {
  background-color: var(--puck-color-grey-11);
  border-color: var(--puck-color-grey-09);
  color: var(--puck-color-grey-04);
  cursor: default;
  opacity: 1;
  outline: 0;
  transition: none;
}

.Input-radioGroupItems {
  display: flex;
  border: 1px solid var(--puck-color-grey-09);
  border-radius: 4px;
  flex-wrap: wrap;
}

.Input-radio {
  border-inline-end: 1px solid var(--puck-color-grey-09);
  flex-grow: 1;
}

.Input-radio:first-of-type {
  border-bottom-left-radius: 4px;
  border-top-left-radius: 4px;
}

.Input-radio:first-of-type .Input-radioInner {
  border-bottom-left-radius: 3px;
  border-top-left-radius: 3px;
}

.Input-radio:last-of-type {
  border-bottom-right-radius: 4px;
  border-inline-end: 0;
  border-top-right-radius: 4px;
}

.Input-radio:last-of-type .Input-radioInner {
  border-bottom-right-radius: 3px;
  border-top-right-radius: 3px;
}

.Input-radioInner {
  background-color: var(--puck-color-white);
  color: var(--puck-color-grey-04);
  cursor: pointer;
  font-size: var(--puck-font-size-xxxs);
  padding: 8px 12px;
  text-align: center;
  transition: background-color 50ms ease-in;
}

.Input-radio:has(:focus-visible) {
  outline: 2px solid var(--puck-color-azure-05);
  outline-offset: 2px;
  position: relative;
}

@media (hover: hover) and (pointer: fine) {
  .Input-radioInner:hover {
    background-color: var(--puck-color-azure-12);
    transition: none;
  }
}

.Input--readOnly .Input-radioInner {
  background-color: var(--puck-color-white);
  color: var(--puck-color-grey-04);
  cursor: default;
}

.Input-radio .Input-radioInput:checked ~ .Input-radioInner {
  background-color: var(--puck-color-azure-11);
  color: var(--puck-color-azure-04);
  font-weight: 500;
}

.Input--readOnly .Input-radioInput:checked ~ .Input-radioInner {
  background-color: var(--puck-color-grey-11);
  color: var(--puck-color-grey-04);
}

.Input-radio .Input-radioInput {
  clip: rect(0 0 0 0);
  clip-path: inset(100%);
  height: 1px;
  overflow: hidden;
  position: absolute;
  white-space: nowrap;
  width: 1px;
}

textarea.Input-input {
  margin-bottom: -4px; /* Remove strange bottom border */
}
