.DropZone {
  --resize-animation-ms: 150ms;

  position: relative;
  height: 100%;
  min-height: var(--min-empty-height);
  outline-offset: -2px;
  width: 100%;
}

.DropZone--hasChildren {
  min-height: 0;
}

.DropZone:empty {
  min-height: var(--min-empty-height);
}

/* We use global data-puck-dragging to avoid re-rendering DropZone */
[data-puck-entry]:not([data-puck-dragging]) .DropZone {
  transition: min-height var(--resize-animation-ms) ease-in;
}

.DropZone--isAreaSelected,
.DropZone--hoveringOverArea:not(.DropZone--isRootZone) {
  background: color-mix(in srgb, var(--puck-color-azure-09) 30%, transparent);
  outline: 2px dashed var(--puck-color-azure-08);
}

.DropZone:empty {
  background: color-mix(in srgb, var(--puck-color-azure-09) 30%, transparent);
  outline: 2px dashed var(--puck-color-azure-08);
}

.DropZone--isDestination {
  outline: 2px dashed var(--puck-color-azure-04) !important;
}

.DropZone--isDestination:not(.DropZone--isRootZone) {
  background: color-mix(
    in srgb,
    var(--puck-color-azure-09) 30%,
    transparent
  ) !important;
}

.DropZone-item {
  position: relative;
}

.DropZone-hitbox {
  position: absolute;
  bottom: -12px;
  height: 24px;
  width: 100%;
  z-index: 1;
}

[data-puck-dragging] .DropZone--isEnabled {
  outline: 2px dashed var(--puck-color-azure-06);
}

.DropZone > *:not([data-puck-component]) {
  opacity: 0;
}

/* Hide overlays if DropZone is animating, which happens during a resize */
body:has(.DropZone--isAnimating:empty) [data-puck-overlay] {
  opacity: 0 !important;
}
