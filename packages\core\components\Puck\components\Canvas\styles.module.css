.PuckCanvas {
  background: var(--puck-color-grey-11);
  display: flex;
  grid-area: editor;
  flex-direction: column;
  padding: var(--puck-space-px);
  overflow: auto;
}

@media (min-width: 1198px) {
  .PuckCanvas {
    padding: calc(var(--puck-space-px) * 1.5);
    padding-top: var(--puck-space-px);
  }

  .PuckCanvas:not(.PuckCanvas:has(.PuckCanvas-controls)) {
    padding-top: calc(var(--puck-space-px) * 1.5);
  }
}

.PuckCanvas-inner {
  display: flex;
  height: 100%;
  justify-content: center;
  min-width: 358px;
  position: relative;
  width: 100%;
}

.PuckCanvas-root {
  background: white;
  border: 1px solid var(--puck-color-grey-09);
  box-sizing: content-box;
  min-width: 321px;
  position: absolute;
  pointer-events: none;
  transform-origin: top;
  top: 0;
  bottom: 0;
  opacity: 0;
}

@media (min-width: 1198px) {
  .PuckCanvas-root {
    min-width: unset;
  }
}

@media (prefers-reduced-motion: reduce) {
  .PuckCanvas-root {
    transition: none !important;
  }
}

.PuckCanvas--ready .PuckCanvas-root {
  pointer-events: unset;
  opacity: 1;
}

.PuckCanvas-loader {
  align-items: center;
  color: var(--puck-color-grey-06);
  display: flex;
  height: 100%;
  justify-content: center;
  transition: opacity 250ms ease-out;
  opacity: 0;
}

.PuckCanvas--showLoader .PuckCanvas-loader {
  opacity: 1;
}

.PuckCanvas--showLoader.PuckCanvas--ready .PuckCanvas-loader {
  opacity: 0;
  height: 0;
  transition: none;
}
