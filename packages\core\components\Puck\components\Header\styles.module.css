.PuckHeader {
  background: var(--puck-color-white);
  border-bottom: 1px solid var(--puck-color-grey-09);
  color: var(--puck-color-black);
  grid-area: header;
  position: relative;
  max-width: 100vw;
}

.PuckHeader-inner {
  align-items: end;
  display: grid;
  gap: var(--puck-space-px);
  grid-template-areas: "left middle right";
  grid-template-columns: 1fr auto 1fr;
  grid-template-rows: auto;
  padding: var(--puck-space-px);
}

.PuckHeader-toggle {
  color: var(--puck-color-grey-05);
  display: flex;
  margin-inline-start: -4px;
  padding-top: 2px;
}

.PuckHeader--rightSideBarVisible .PuckHeader-rightSideBarToggle,
.PuckHeader--leftSideBarVisible .PuckHeader-leftSideBarToggle {
  color: var(--puck-color-black);
}

.PuckHeader-title {
  align-self: center;
}

.PuckHeader-path {
  font-family: var(--puck-font-family-monospaced);
  font-size: var(--puck-font-size-xxs);
  font-weight: normal;
  word-break: break-all;
}

.PuckHeader-tools {
  display: flex;
  gap: 16px;
  justify-content: flex-end;
}

.PuckHeader-menuButton {
  color: var(--puck-color-grey-05);
  margin-inline-start: -4px;
}

.PuckHeader--menuOpen .PuckHeader-menuButton {
  color: var(--puck-color-black);
}

@media (min-width: 638px) {
  .PuckHeader-menuButton {
    display: none;
  }
}
