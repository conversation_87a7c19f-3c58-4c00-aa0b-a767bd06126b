/*
 * <PERSON><PERSON>'s responsive layout uses minimum viewport widths slightly _below_ common
 * framework/device breakpoints, and ensures that the width of the resulting
 * Puck page preview (zoomed at 75%) is slightly _above_ common framework/device
 * breakpoints. This can help alleviate some of the pain when editing responsive
 * pages in a preview area that is narrower than the reported viewport width.
 *
 * Viewport | Puck page @ zoom 0.75
 * --------------------------------
 * -        | 322px
 * 766px    | 322px
 * 990px    | 604px
 * 1022px   | 646px
 * 1198px   | 801px
 * 1398px   | 1025px
 * 1598px   | 1212px
 */

.Puck {
  --puck-space-px: 16px;
  font-family: var(--puck-font-family);
  overflow-x: hidden;
}

@media (min-width: 766px) {
  .Puck {
    overflow-x: auto;
  }
}

.Puck-portal {
  position: relative;
  z-index: 2;
}

/* Puck Layout */

.PuckLayout-inner {
  --puck-frame-width: auto;
  --puck-side-bar-width: 0px;
  display: grid;
  grid-template-areas: "header header header" "left editor right";
  grid-template-columns: 0 var(--puck-frame-width) 0;
  grid-template-rows: min-content auto;
  height: 100dvh;
  position: relative;
  z-index: 0;
}

.PuckLayout--mounted .PuckLayout-inner {
  --puck-side-bar-width: 186px;
}

.PuckLayout--leftSideBarVisible .PuckLayout-inner {
  grid-template-columns:
    var(--puck-side-bar-width) var(--puck-frame-width)
    0;
}

.PuckLayout--rightSideBarVisible .PuckLayout-inner {
  grid-template-columns:
    0 var(--puck-frame-width)
    var(--puck-side-bar-width);
}

.PuckLayout--leftSideBarVisible.PuckLayout--rightSideBarVisible
  .PuckLayout-inner {
  grid-template-columns:
    var(--puck-side-bar-width) var(--puck-frame-width)
    var(--puck-side-bar-width);
}

@media (min-width: 458px) {
  .PuckLayout-mounted .PuckLayout-inner {
    --puck-frame-width: minmax(266px, auto);
  }
}

@media (min-width: 638px) {
  .PuckLayout .PuckLayout-inner {
    --puck-side-bar-width: minmax(186px, 250px);
  }
}

@media (min-width: 766px) {
  .PuckLayout .PuckLayout-inner {
    --puck-frame-width: auto;
  }
}

@media (min-width: 990px) {
  .PuckLayout .PuckLayout-inner {
    --puck-side-bar-width: 256px;
  }
}

@media (min-width: 1198px) {
  .PuckLayout .PuckLayout-inner {
    --puck-side-bar-width: 274px;
  }
}

@media (min-width: 1398px) {
  .PuckLayout .PuckLayout-inner {
    --puck-side-bar-width: 290px;
  }
}

@media (min-width: 1598px) {
  .PuckLayout .PuckLayout-inner {
    --puck-side-bar-width: 320px;
  }
}

.PuckLayout-leftSideBar {
  background: var(--puck-color-grey-12);
  border-inline-end: 1px solid var(--puck-color-grey-09);
  display: flex;
  flex-direction: column;
  grid-area: left;
  overflow-y: auto;
}

.PuckLayout-rightSideBar {
  background: var(--puck-color-white);
  border-inline-start: 1px solid var(--puck-color-grey-09);
  display: flex;
  flex-direction: column;
  grid-area: right;
  overflow-y: auto;
}
