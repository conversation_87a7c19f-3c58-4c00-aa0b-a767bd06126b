{"name": "@measured/puck", "version": "0.19.1", "author": "<PERSON> <<EMAIL>>", "repository": "measuredco/puck", "bugs": "https://github.com/measuredco/puck/issues", "homepage": "https://puckeditor.com", "private": false, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"react-server": {"types": "./dist/rsc.d.ts", "import": "./dist/rsc.mjs", "require": "./dist/rsc.js"}, "default": {"types": "./dist/index.d.ts", "import": "./dist/index.mjs", "require": "./dist/index.js"}}, "./rsc": {"types": "./dist/rsc.d.ts", "import": "./dist/rsc.mjs", "require": "./dist/rsc.js"}, "./puck.css": "./dist/index.css", "./dist/index.css": "./dist/index.css", "./package.json": "./package.json"}, "typesVersions": {"*": {"rsc": ["./dist/rsc.js"]}}, "license": "MIT", "scripts": {"lint": "eslint \"**/*.ts*\"", "build": "rm -rf dist && tsup index.ts rsc.tsx", "test": "jest", "prepare": "cp ../../README.md . && yarn build", "postpublish": "rm README.md"}, "files": ["dist"], "devDependencies": {"@juggle/resize-observer": "^3.4.0", "@testing-library/jest-dom": "^6.6.3", "@testing-library/react": "^16.1.0", "@types/deep-diff": "^1.0.3", "@types/flat": "^5.0.5", "@types/jest": "^29.5.14", "@types/object-hash": "^3.0.6", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "@types/uuid": "^10.0.0", "css-box-model": "^1.2.1", "eslint": "^7.32.0", "eslint-config-custom": "*", "identity-obj-proxy": "^3.0.0", "jest": "^29.7.0", "jest-environment-jsdom": "^30.0.0-beta.3", "lucide-react": "^0.468.0", "ts-jest": "^29.3.4", "tsconfig": "*", "tsup": "^8.2.4", "tsup-config": "*", "typescript": "^5.5.4"}, "dependencies": {"@dnd-kit/helpers": "0.1.18", "@dnd-kit/react": "0.1.18", "deep-diff": "^1.0.2", "fast-deep-equal": "^3.1.3", "flat": "^5.0.2", "object-hash": "^3.0.0", "react-hotkeys-hook": "^4.6.1", "use-debounce": "^9.0.4", "uuid": "^9.0.1", "zustand": "^5.0.3"}, "peerDependencies": {"react": "^18.0.0 || ^19.0.0"}}