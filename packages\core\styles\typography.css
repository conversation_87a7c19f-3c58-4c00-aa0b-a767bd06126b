:root {
  /**
     * Modular Scale
     *
     * 2:1 - octave - 2.0
     *
     * Base: 12px
     *
     * Interval: 5
     *
     * http://spencermortensen.com/articles/typographic-scale/
     *
     * Spacing unit: 16px * 1.5 = 24px
     *
     * Root/html font-size is undefined (browser default)
     *
     * 1rem = 16px
     */

  /* Unitless font sizes
       ======================================================================== */

  --puck-font-size-scale-base-unitless: 12;
  --puck-font-size-xxxs-unitless: 12;
  --puck-font-size-xxs-unitless: 14;
  --puck-font-size-xs-unitless: 16;
  --puck-font-size-s-unitless: 18;
  --puck-font-size-m-unitless: 21;
  --puck-font-size-l-unitless: 24;
  --puck-font-size-xl-unitless: 28;
  --puck-font-size-xxl-unitless: 36;
  --puck-font-size-xxxl-unitless: 48;
  --puck-font-size-xxxxl-unitless: 56;

  /* Descriptive font sizes
       ======================================================================== */

  --puck-font-size-xxxs: calc(1rem * var(--puck-font-size-xxxs-unitless) / 16);
  --puck-font-size-xxs: calc(1rem * var(--puck-font-size-xxs-unitless) / 16);
  --puck-font-size-xs: calc(1rem * var(--puck-font-size-xs-unitless) / 16);
  --puck-font-size-s: calc(1rem * var(--puck-font-size-s-unitless) / 16);
  --puck-font-size-m: calc(1rem * var(--puck-font-size-m-unitless) / 16);
  --puck-font-size-l: calc(1rem * var(--puck-font-size-l-unitless) / 16);
  --puck-font-size-xl: calc(1rem * var(--puck-font-size-xl-unitless) / 16);
  --puck-font-size-xxl: calc(1rem * var(--puck-font-size-xxl-unitless) / 16);
  --puck-font-size-xxxl: calc(1rem * var(--puck-font-size-xxxl-unitless) / 16);
  --puck-font-size-xxxxl: calc(
    1rem * var(--puck-font-size-xxxxl-unitless) / 16
  );

  /* Functional font sizes
      ========================================================================= */

  --puck-font-size-base: var(--puck-font-size-xs);

  /* Descriptive line heights
      ========================================================================= */

  --line-height-reset: 1;
  --line-height-xs: calc(
    var(--space-m-unitless) / var(--puck-font-size-m-unitless)
  );
  --line-height-s: calc(
    var(--space-m-unitless) / var(--puck-font-size-s-unitless)
  );
  --line-height-m: calc(
    var(--space-m-unitless) / var(--puck-font-size-xs-unitless)
  );
  --line-height-l: calc(
    var(--space-m-unitless) / var(--puck-font-size-xxs-unitless)
  );
  --line-height-xl: calc(
    var(--space-m-unitless) / var(--puck-font-size-scale-base-unitless)
  );

  /* Functional line heights
      ========================================================================= */

  --line-height-base: var(--line-height-m);

  /* Font families
  ======================================================================== */

  --fallback-font-stack: -apple-system, BlinkMacSystemFont, Segoe UI,
    Helvetica Neue, sans-serif, Apple Color Emoji, Segoe UI Emoji,
    Segoe UI Symbol;

  --puck-font-family: Inter, var(--fallback-font-stack);
  --puck-font-family-monospaced: ui-monospace, "Cascadia Code",
    "Source Code Pro", Menlo, Consolas, "DejaVu Sans Mono", monospace;
}

@supports (font-variation-settings: normal) {
  :root {
    --puck-font-family: InterVariable, var(--fallback-font-stack);
  }
}
