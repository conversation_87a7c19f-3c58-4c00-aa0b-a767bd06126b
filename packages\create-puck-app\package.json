{"name": "create-puck-app", "version": "0.19.1", "author": "<PERSON> <<EMAIL>>", "repository": "measuredco/puck", "bugs": "https://github.com/measuredco/puck/issues", "homepage": "https://puckeditor.com", "private": false, "license": "MIT", "type": "module", "bin": {"create-puck-app": "./index.js"}, "files": ["templates", "index.js"], "scripts": {"generate": "node scripts/generate.js", "prepublishOnly": "yarn generate", "removeGitignore": "mv templates/.gitignore templates/gitignore", "restoreGitignore": "mv templates/gitignore templates/.gitignore"}, "dependencies": {"commander": "^10.0.1", "glob": "^10.3.4", "handlebars": "^4.7.7", "inquirer": "^9.2.7", "prettier": "^2.8.8"}}