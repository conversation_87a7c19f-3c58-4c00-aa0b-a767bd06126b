{"name": "{{appName}}", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "react-router build", "dev": "react-router dev", "start": "react-router-serve ./build/server/index.js", "typecheck": "react-router typegen && tsc"}, "dependencies": {"@measured/puck": "{{puck<PERSON><PERSON><PERSON>}}", "@react-router/node": "^7.5.3", "@react-router/serve": "^7.5.3", "isbot": "^5", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router": "^7.5.3"}, "devDependencies": {"@react-router/dev": "^7.5.3", "@types/node": "^20", "@types/react": "^19.1.2", "@types/react-dom": "^19.1.2", "typescript": "^5.8.3", "vite": "^6.3.3", "vite-tsconfig-paths": "^5.1.4"}, "engines": {"node": ">=20.0.0"}}