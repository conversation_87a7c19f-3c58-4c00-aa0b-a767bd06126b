{"name": "{{appName}}", "version": "1.0.0", "private": true, "sideEffects": false, "type": "module", "scripts": {"build": "remix build", "dev": "remix dev --manual", "start": "remix-serve ./build/index.js", "typecheck": "tsc"}, "dependencies": {"@measured/puck": "{{puck<PERSON><PERSON><PERSON>}}", "@remix-run/css-bundle": "^2.2.0", "@remix-run/node": "^2.2.0", "@remix-run/react": "^2.2.0", "@remix-run/serve": "^2.2.0", "isbot": "^3.6.8", "react": "^18.2.0", "react-dom": "^18.2.0"}, "devDependencies": {"@remix-run/dev": "^2.2.0", "@remix-run/eslint-config": "^2.2.0", "@types/react": "^18.2.20", "@types/react-dom": "^18.2.7", "eslint": "^8.38.0", "typescript": "^5.1.6"}, "engines": {"node": ">=20.0.0"}}