{"name": "@measured/puck-plugin-heading-analyzer", "version": "0.19.1", "author": "<PERSON> <<EMAIL>>", "repository": "measuredco/puck", "bugs": "https://github.com/measuredco/puck/issues", "homepage": "https://puckeditor.com", "private": false, "main": "./dist/index.js", "types": "./dist/index.d.ts", "exports": {".": {"import": "./dist/index.mjs", "types": "./dist/index.d.ts"}, "./dist/index.css": "./dist/index.css"}, "license": "MIT", "scripts": {"lint": "eslint \"**/*.ts*\"", "build": "rm -rf dist && tsup index.ts", "prepare": "yarn build"}, "files": ["dist"], "devDependencies": {"@measured/puck": "^0.19.1", "@types/react": "^19.0.1", "@types/react-dom": "^19.0.2", "eslint": "^7.32.0", "eslint-config-custom": "*", "tsconfig": "*", "tsup": "^8.2.4", "tsup-config": "*", "typescript": "^5.5.4"}, "dependencies": {"react-from-json": "^0.8.0"}, "peerDependencies": {"react": "^17.0.0 || ^18.0.0 || ^19.0.0"}}